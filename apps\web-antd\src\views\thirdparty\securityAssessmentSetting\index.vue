<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { AssessmentEnterpriseRelationsApi } from '#/api/thirdparty/securityAssessmentSetting';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromBlobPart, isEmpty } from '@vben/utils';

import { message } from 'ant-design-vue';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteAssessmentEnterpriseRelations,
  deleteAssessmentEnterpriseRelationsListByIds,
  exportAssessmentEnterpriseRelations,
  getAssessmentEnterpriseRelationsPage,
} from '#/api/thirdparty/securityAssessmentSetting';
import { $t } from '#/locales';

import { useGridColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';
import PublishModalComponent from './modules/publish-modal.vue';
import RevokeModalComponent from './modules/revoke-modal.vue';

const router = useRouter();

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

const [PublishModal, publishModalApi] = useVbenModal({
  connectedComponent: PublishModalComponent,
  destroyOnClose: true,
});

const [RevokeModal, revokeModalApi] = useVbenModal({
  connectedComponent: RevokeModalComponent,
  destroyOnClose: true,
});

/** 刷新表格 */
function onRefresh() {
  gridApi.query();
}

/** 创建安全评估-企业关联 */
function handleCreate() {
  formModalApi.setData({}).open();
}

/** 编辑安全评估-企业关联 */
function handleEdit(
  row: AssessmentEnterpriseRelationsApi.AssessmentEnterpriseRelations,
) {
  formModalApi.setData(row).open();
}

/** 发布安全评估任务 */
function handlePublish(
  row: AssessmentEnterpriseRelationsApi.AssessmentEnterpriseRelations,
) {
  publishModalApi.setData(row).open();
}

/** 撤销安全评估任务 */
function handleRevoke(
  row: AssessmentEnterpriseRelationsApi.AssessmentEnterpriseRelations,
) {
  revokeModalApi.setData(row).open();
}

/** 查看安全评估详情 */
function handleView(
  row: AssessmentEnterpriseRelationsApi.AssessmentEnterpriseRelations,
) {
  router.push(`/thirdparty/securityAssessmentSetting/detail/${row.id}`);
}

/** 删除安全评估-企业关联 */
async function handleDelete(
  row: AssessmentEnterpriseRelationsApi.AssessmentEnterpriseRelations,
) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.id]),
    key: 'action_key_msg',
  });
  try {
    await deleteAssessmentEnterpriseRelations(row.id as number);
    message.success({
      content: $t('ui.actionMessage.deleteSuccess', [row.id]),
      key: 'action_key_msg',
    });
    onRefresh();
  } finally {
    hideLoading();
  }
}

/** 批量删除安全评估-企业关联 */
async function handleDeleteBatch() {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting'),
    key: 'action_key_msg',
  });
  try {
    await deleteAssessmentEnterpriseRelationsListByIds(deleteIds.value);
    message.success({
      content: $t('ui.actionMessage.deleteSuccess'),
      key: 'action_key_msg',
    });
    onRefresh();
  } finally {
    hideLoading();
  }
}

const deleteIds = ref<number[]>([]); // 待删除安全评估-企业关联 ID
function setDeleteIds({
  records,
}: {
  records: AssessmentEnterpriseRelationsApi.AssessmentEnterpriseRelations[];
}) {
  deleteIds.value = records.map((item) => item.id);
}

/** 导出表格 */
async function handleExport() {
  const data = await exportAssessmentEnterpriseRelations(
    await gridApi.formApi.getValues(),
  );
  downloadFileFromBlobPart({ fileName: '安全评估-企业关联.xls', source: data });
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    pagerConfig: {
      enabled: true,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getAssessmentEnterpriseRelationsPage({
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
      isHover: true,
    },
    toolbarConfig: {
      refresh: { code: 'query' },
      search: true,
    },
  } as VxeTableGridOptions<AssessmentEnterpriseRelationsApi.AssessmentEnterpriseRelations>,
  gridEvents: {
    checkboxAll: setDeleteIds,
    checkboxChange: setDeleteIds,
  },
});
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <PublishModal @success="onRefresh" />
    <RevokeModal @success="onRefresh" />

    <Grid table-title="安全评估-企业关联列表">
      <template #toolbar-tools>
        <TableAction
          :actions="[
            {
              label: $t('ui.actionTitle.create', ['安全评估-企业关联']),
              type: 'primary',
              icon: ACTION_ICON.ADD,
              auth: ['system:assessment-enterprise-relations:create'],
              onClick: handleCreate,
            },
            {
              label: $t('ui.actionTitle.export'),
              type: 'primary',
              icon: ACTION_ICON.DOWNLOAD,
              auth: ['system:assessment-enterprise-relations:export'],
              onClick: handleExport,
            },
            {
              label: $t('ui.actionTitle.deleteBatch'),
              type: 'primary',
              danger: true,
              icon: ACTION_ICON.DELETE,
              disabled: isEmpty(deleteIds),
              auth: ['system:assessment-enterprise-relations:delete'],
              onClick: handleDeleteBatch,
            },
          ]"
        />
      </template>
      <template #actions="{ row }">
        <TableAction
          :actions="[
            {
              label: $t('common.edit'),
              type: 'link',
              icon: ACTION_ICON.EDIT,
              auth: ['system:assessment-enterprise-relations:update'],
              onClick: handleEdit.bind(null, row),
            },
            {
              label: '详情',
              type: 'link',
              icon: ACTION_ICON.VIEW,
              onClick: handleView.bind(null, row),
            },
            ...(row.status === 'initial'
              ? [
                  {
                    label: '发布',
                    type: 'link' as const,
                    icon: ACTION_ICON.VIEW,
                    onClick: handlePublish.bind(null, row),
                  },
                ]
              : []),
            ...(row.status === 'published'
              ? [
                  {
                    label: '撤销',
                    type: 'link' as const,
                    icon: ACTION_ICON.DELETE,
                    onClick: handleRevoke.bind(null, row),
                  },
                ]
              : []),
            {
              label: $t('common.delete'),
              type: 'link',
              danger: true,
              icon: ACTION_ICON.DELETE,
              auth: ['system:assessment-enterprise-relations:delete'],
              popConfirm: {
                title: $t('ui.actionMessage.deleteConfirm', [row.id]),
                confirm: handleDelete.bind(null, row),
              },
            },
          ]"
        />
      </template>
    </Grid>
  </Page>
</template>
