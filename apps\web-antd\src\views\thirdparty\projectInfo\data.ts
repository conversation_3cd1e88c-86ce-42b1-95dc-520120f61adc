import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ProjectInfoApi } from '#/api/thirdparty/projectInfo';

import { handleTree } from '@vben/utils';

import { getDeptList } from '#/api/system/dept';

// 文件类型枚举
export enum FileTypeEnum {
  EXCEL = 'excel',
  IMAGE = 'image',
  PDF = 'pdf',
  UNKNOWN = 'unknown',
  WORD = 'word',
}

// 预览插件类型
export interface PreviewPlugin {
  type: FileTypeEnum;
  name: string;
  component?: string;
  supportedExtensions: string[];
}

// 预览插件配置
export const PREVIEW_PLUGINS: PreviewPlugin[] = [
  {
    type: FileTypeEnum.IMAGE,
    name: '图片预览',
    component: 'ImagePreview',
    supportedExtensions: ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'],
  },
  {
    type: FileTypeEnum.PDF,
    name: 'PDF预览',
    component: 'PdfPreview',
    supportedExtensions: ['.pdf'],
  },
  {
    type: FileTypeEnum.EXCEL,
    name: 'Excel预览',
    component: 'ExcelPreview',
    supportedExtensions: ['.xlsx', '.xls', '.csv'],
  },
  {
    type: FileTypeEnum.WORD,
    name: 'Word预览',
    component: 'WordPreview',
    supportedExtensions: ['.docx', '.doc'],
  },
];

/**
 * 从文件URL或文件名中提取文件扩展名
 * @param fileUrl 文件URL或文件名
 * @returns 文件扩展名（小写，包含点号）
 */
export function getFileExtension(fileUrl: string): string {
  try {
    if (!fileUrl) {
      return '';
    }

    // 移除查询参数
    const urlWithoutQuery = fileUrl.split('?')[0];
    if (!urlWithoutQuery) {
      return '';
    }

    // 获取文件名部分
    const fileName = urlWithoutQuery.split('/').pop() || '';
    // 提取扩展名
    const lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex === -1) {
      return '';
    }
    return fileName.slice(Math.max(0, lastDotIndex)).toLowerCase();
  } catch (error) {
    console.warn('获取文件扩展名失败:', error);
    return '';
  }
}

/**
 * 根据文件扩展名检测文件类型
 * @param extension 文件扩展名
 * @returns 文件类型
 */
export function detectFileType(extension: string): FileTypeEnum {
  const normalizedExt = extension.toLowerCase();

  for (const plugin of PREVIEW_PLUGINS) {
    if (plugin.supportedExtensions.includes(normalizedExt)) {
      return plugin.type;
    }
  }

  return FileTypeEnum.UNKNOWN;
}

/**
 * 根据文件URL获取对应的预览插件
 * @param fileUrl 文件URL
 * @returns 预览插件配置
 */
export function getPreviewPlugin(fileUrl: string): null | PreviewPlugin {
  try {
    const extension = getFileExtension(fileUrl);
    if (!extension) {
      return null;
    }

    const fileType = detectFileType(extension);
    return PREVIEW_PLUGINS.find((plugin) => plugin.type === fileType) || null;
  } catch (error) {
    console.error('获取预览插件失败:', error);
    return null;
  }
}

/**
 * 检查文件是否支持预览
 * @param fileUrl 文件URL
 * @returns 是否支持预览
 */
export function isFilePreviewable(fileUrl: string): boolean {
  const plugin = getPreviewPlugin(fileUrl);
  return plugin !== null;
}

/**
 * 获取文件类型的显示名称
 * @param fileUrl 文件URL
 * @returns 文件类型显示名称
 */
export function getFileTypeDisplayName(fileUrl: string): string {
  const plugin = getPreviewPlugin(fileUrl);
  if (plugin) {
    return plugin.name;
  }

  const extension = getFileExtension(fileUrl);
  return extension ? `${extension.toUpperCase()} 文件` : '未知文件';
}

/**
 * 预览文件
 * @param fileUrl 文件URL
 * @param _fileName 文件名称（保留参数以备将来使用）
 * @returns 预览结果
 */
export function previewFile(
  fileUrl: string,
  _fileName?: string,
): {
  message?: string;
  plugin?: PreviewPlugin;
  success: boolean;
} {
  try {
    const plugin = getPreviewPlugin(fileUrl);

    if (!plugin) {
      return {
        success: false,
        message: `不支持预览此类型的文件: ${getFileTypeDisplayName(fileUrl)}`,
      };
    }

    return {
      success: true,
      plugin,
      message: `使用 ${plugin.name} 预览文件`,
    };
  } catch (error) {
    console.error('预览文件失败:', error);
    return {
      success: false,
      message: '预览文件时发生错误',
    };
  }
}

/**
 * 获取文件类型对应的图标
 * @param fileUrl 文件URL
 * @returns 图标类名
 */
export function getFileTypeIcon(fileUrl: string): string {
  const fileType = detectFileType(getFileExtension(fileUrl));

  const iconMap: Record<FileTypeEnum, string> = {
    [FileTypeEnum.IMAGE]: 'i-mdi:file-image',
    [FileTypeEnum.PDF]: 'i-mdi:file-pdf-box',
    [FileTypeEnum.EXCEL]: 'i-mdi:file-excel',
    [FileTypeEnum.WORD]: 'i-mdi:file-word',
    [FileTypeEnum.UNKNOWN]: 'i-mdi:file-document',
  };

  return iconMap[fileType] || iconMap[FileTypeEnum.UNKNOWN];
}

/** 新增/修改的表单 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'id',
      component: 'Input',
      dependencies: {
        triggerFields: [''],
        show: () => false,
      },
    },
    {
      fieldName: 'projectName',
      label: '项目名称',
      rules: 'required',
      component: 'Input',
      componentProps: {
        placeholder: '请输入项目名称',
      },
    },
    {
      fieldName: 'serviceType',
      label: '服务类型',
      rules: 'required',
      component: 'Input',
      componentProps: {
        placeholder: '请选择服务类型',
      },
    },
    {
      fieldName: 'serviceObject',
      label: '服务对象',
      rules: 'required',
      component: 'ApiTreeSelect',
      componentProps: {
        api: async () => {
          const data = await getDeptList();
          return handleTree(data);
        },
        labelField: 'name',
        valueField: 'id',
        childrenField: 'children',
        placeholder: '请选择服务对象',
        treeDefaultExpandAll: true,
      },
    },
    {
      fieldName: 'startTime',
      label: '开始时间',
      rules: 'required',
      component: 'DatePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'x',
      },
    },
    {
      fieldName: 'endTime',
      label: '结束时间',
      rules: 'required',
      component: 'DatePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'x',
      },
    },
    // {
    //   fieldName: 'enterpriseId',
    //   label: '企业信息ID',
    //   rules: 'required',
    //   component: 'Input',
    //   componentProps: {
    //     placeholder: '请输入企业信息ID',
    //   },
    // },
    {
      fieldName: 'enterpriseName',
      label: '企业名称',
      rules: 'required',
      component: 'EnterpriseSelect',
      componentProps: {
        placeholder: '请选择企业',
      },
    },
    {
      fieldName: 'responsiblePerson',
      label: '负责人',
      component: 'Input',
      componentProps: {
        disabled: true,
        placeholder: '请输入负责人',
      },
    },
    {
      fieldName: 'contactPhone',
      label: '联系电话',
      component: 'Input',
      componentProps: {
        placeholder: '请输入联系电话',
      },
    },
    {
      fieldName: 'remark',
      label: '备注',
      component: 'Input',
      componentProps: {
        placeholder: '请输入备注',
      },
    },
    {
      fieldName: 'projectNotice',
      label: '项目告知书',
      component: 'FileUpload',
    },
  ];
}

/** 列表的搜索表单 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'projectName',
      label: '项目名称',
      component: 'Input',
      componentProps: {
        allowClear: true,
        placeholder: '请输入项目名称',
      },
    },
    {
      fieldName: 'serviceType',
      label: '服务类型',
      component: 'Input',
      componentProps: {
        allowClear: true,
        placeholder: '请选择服务类型',
      },
    },
    {
      fieldName: 'serviceObject',
      label: '服务对象',
      component: 'ApiTreeSelect',
      componentProps: {
        api: async () => {
          const data = await getDeptList();
          return handleTree(data);
        },
        allowClear: true,
        labelField: 'name',
        valueField: 'id',
        childrenField: 'children',
        placeholder: '请选择服务对象',
        treeDefaultExpandAll: true,
      },
    },
    // {
    //   fieldName: 'startTime',
    //   label: '开始时间',
    //   component: 'RangePicker',
    //   componentProps: {
    //     ...getRangePickerDefaultProps(),
    //     allowClear: true,
    //   },
    // },
    // {
    //   fieldName: 'endTime',
    //   label: '结束时间',
    //   component: 'RangePicker',
    //   componentProps: {
    //     ...getRangePickerDefaultProps(),
    //     allowClear: true,
    //   },
    // },
    // {
    //   fieldName: 'enterpriseId',
    //   label: '企业信息ID',
    //   component: 'Input',
    //   componentProps: {
    //     allowClear: true,
    //     placeholder: '请输入企业信息ID',
    //   },
    // },
    {
      fieldName: 'enterpriseName',
      label: '企业名称',
      component: 'Input',
      componentProps: {
        placeholder: '请选择企业',
      },
    },
    {
      fieldName: 'responsiblePerson',
      label: '负责人',
      component: 'Input',
      componentProps: {
        allowClear: true,
        placeholder: '请输入负责人',
      },
    },
    // {
    //   fieldName: 'contactPhone',
    //   label: '联系电话',
    //   component: 'Input',
    //   componentProps: {
    //     allowClear: true,
    //     placeholder: '请输入联系电话',
    //   },
    // },
    // {
    //   fieldName: 'remark',
    //   label: '备注',
    //   component: 'Input',
    //   componentProps: {
    //     allowClear: true,
    //     placeholder: '请输入备注',
    //   },
    // },
    // {
    //   fieldName: 'projectNotice',
    //   label: '项目告知书（附件路径）',
    //   component: 'Input',
    //   componentProps: {
    //     allowClear: true,
    //     placeholder: '请输入项目告知书（附件路径）',
    //   },
    // },
    // {
    //   fieldName: 'createTime',
    //   label: '创建时间',
    //   component: 'RangePicker',
    //   componentProps: {
    //     ...getRangePickerDefaultProps(),
    //     allowClear: true,
    //   },
    // },
  ];
}

/** 列表的字段 */
export function useGridColumns(): VxeTableGridOptions<ProjectInfoApi.ProjectInfo>['columns'] {
  return [
    { type: 'checkbox', width: 40 },
    // {
    //   field: 'id',
    //   title: '项目ID',
    //   minWidth: 120,
    // },
    {
      field: 'enterpriseName',
      title: '企业名称',
      minWidth: 120,
    },
    {
      field: 'responsiblePerson',
      title: '负责人',
      minWidth: 120,
    },
    {
      field: 'projectName',
      title: '项目名称',
      minWidth: 120,
    },
    {
      field: 'serviceType',
      title: '服务类型',
      minWidth: 120,
    },
    {
      field: 'serviceObject',
      title: '服务对象',
      minWidth: 120,
    },
    {
      field: 'startTime',
      title: '开始时间',
      minWidth: 120,
      formatter: 'formatDateTime',
    },
    {
      field: 'endTime',
      title: '结束时间',
      minWidth: 120,
      formatter: 'formatDateTime',
    },
    // {
    //   field: 'enterpriseId',
    //   title: '企业信息ID',
    //   minWidth: 120,
    // },
    // {
    //   field: 'contactPhone',
    //   title: '联系电话',
    //   minWidth: 120,
    // },
    // {
    //   field: 'remark',
    //   title: '备注',
    //   minWidth: 120,
    // },
    {
      field: 'projectNotice',
      title: '项目告知书',
      minWidth: 120,
      cellRender: {
        name: 'CellFileIcon',
      },
    },
    // {
    //   field: 'createTime',
    //   title: '创建时间',
    //   minWidth: 120,
    //   formatter: 'formatDateTime',
    // },
    {
      title: '操作',
      width: 200,
      fixed: 'right',
      slots: { default: 'actions' },
    },
  ];
}
