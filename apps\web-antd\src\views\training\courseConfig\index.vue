<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { CourseConfigApi } from '#/api/training/courseConfig';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromBlobPart, isEmpty } from '@vben/utils';

import { message } from 'ant-design-vue';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteCourseConfig,
  deleteCourseConfigListByIds,
  exportCourseConfig,
  getCourseConfigPage,
} from '#/api/training/courseConfig';
import { $t } from '#/locales';

import { useGridColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';
import VideoPlayer from './modules/video-player.vue';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

const [VideoPlayerModal, videoPlayerModalApi] = useVbenModal({
  connectedComponent: VideoPlayer,
  destroyOnClose: true,
});

/** 刷新表格 */
function onRefresh() {
  gridApi.query();
}

/** 创建课程配置信息 */
function handleCreate() {
  formModalApi.setData({}).open();
}

/** 编辑课程配置信息 */
function handleEdit(row: CourseConfigApi.CourseConfig) {
  formModalApi.setData(row).open();
}

/** 删除课程配置信息 */
async function handleDelete(row: CourseConfigApi.CourseConfig) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.id]),
    key: 'action_key_msg',
  });
  try {
    await deleteCourseConfig(row.id as number);
    message.success({
      content: $t('ui.actionMessage.deleteSuccess', [row.id]),
      key: 'action_key_msg',
    });
    onRefresh();
  } finally {
    hideLoading();
  }
}

/** 批量删除课程配置信息 */
async function handleDeleteBatch() {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting'),
    key: 'action_key_msg',
  });
  try {
    await deleteCourseConfigListByIds(deleteIds.value);
    message.success({
      content: $t('ui.actionMessage.deleteSuccess'),
      key: 'action_key_msg',
    });
    onRefresh();
  } finally {
    hideLoading();
  }
}

/** 学习课程 */
function handleLearn(row: CourseConfigApi.CourseConfig) {
  videoPlayerModalApi.setData(row).open();
}

const deleteIds = ref<number[]>([]); // 待删除课程配置信息 ID
function setDeleteIds({
  records,
}: {
  records: CourseConfigApi.CourseConfig[];
}) {
  deleteIds.value = records.map((item) => item.id);
}

/** 导出表格 */
async function handleExport() {
  const data = await exportCourseConfig(await gridApi.formApi.getValues());
  downloadFileFromBlobPart({ fileName: '课程配置信息.xls', source: data });
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    pagerConfig: {
      enabled: true,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getCourseConfigPage({
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
      isHover: true,
    },
    toolbarConfig: {
      refresh: { code: 'query' },
      search: true,
    },
  } as VxeTableGridOptions<CourseConfigApi.CourseConfig>,
  gridEvents: {
    checkboxAll: setDeleteIds,
    checkboxChange: setDeleteIds,
  },
});
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <VideoPlayerModal @success="onRefresh" />

    <Grid table-title="课程配置信息列表">
      <template #toolbar-tools>
        <TableAction
          :actions="[
            {
              label: $t('ui.actionTitle.create', ['课程配置信息']),
              type: 'primary',
              icon: ACTION_ICON.ADD,
              auth: ['training:course-config:create'],
              onClick: handleCreate,
            },
            {
              label: $t('ui.actionTitle.export'),
              type: 'primary',
              icon: ACTION_ICON.DOWNLOAD,
              auth: ['training:course-config:export'],
              onClick: handleExport,
            },
            {
              label: $t('ui.actionTitle.deleteBatch'),
              type: 'primary',
              danger: true,
              icon: ACTION_ICON.DELETE,
              disabled: isEmpty(deleteIds),
              auth: ['training:course-config:delete'],
              onClick: handleDeleteBatch,
            },
          ]"
        />
      </template>
      <template #actions="{ row }">
        <TableAction
          :actions="[
            {
              label: $t('common.edit'),
              type: 'link',
              icon: ACTION_ICON.EDIT,
              auth: ['training:course-config:update'],
              onClick: handleEdit.bind(null, row),
            },
            {
              label: '学习',
              type: 'link',
              icon: ACTION_ICON.VIEW,
              auth: ['training:course-config:update'],
              onClick: handleLearn.bind(null, row),
            },
            {
              label: $t('common.delete'),
              type: 'link',
              danger: true,
              icon: ACTION_ICON.DELETE,
              auth: ['training:course-config:delete'],
              popConfirm: {
                title: $t('ui.actionMessage.deleteConfirm', [row.courseName]),
                confirm: handleDelete.bind(null, row),
              },
            },
          ]"
        />
      </template>
    </Grid>
  </Page>
</template>
