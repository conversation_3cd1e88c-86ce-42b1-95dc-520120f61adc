<script lang="ts" setup>
import type { FireRecordApi } from '#/api/thirdparty/fireRecord';

import { ref, computed } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Descriptions, Card, Divider } from 'ant-design-vue';
import { formatDateTime } from '@vben/utils';
import { DictTag } from '#/components/dict-tag';
import { DICT_TYPE } from '#/utils';

import { getFireRecord } from '#/api/thirdparty/fireRecord';

const detailData = ref<FireRecordApi.FireRecord>();

const getTitle = computed(() => {
  return detailData.value ? `火情记录详情 - ${detailData.value.enterpriseName || ''}` : '火情记录详情';
});

const [Modal, modalApi] = useVbenModal({
  showConfirmButton: false,
  showCancelButton: false,
  async onOpenChange(isOpen: boolean) {
    if (!isOpen) {
      detailData.value = undefined;
      return;
    }
    // 获取详情数据
    const data = modalApi.getData<FireRecordApi.FireRecord>();
    if (!data) {
      return;
    }
    modalApi.lock();
    try {
      // 加载详情数据
      detailData.value = data.id ? await getFireRecord(data.id) : data;
    } finally {
      modalApi.unlock();
    }
  },
});

// 状态颜色映射
const getStatusColor = (status: string) => {
  const statusMap: Record<string, string> = {
    '红': 'red',
    '黄': 'orange',
  };
  return statusMap[status] || 'default';
};

// 状态文本映射
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '红': '红色',
    '黄': '黄色',
  };
  return statusMap[status] || status;
};
</script>

<template>
  <Modal :title="getTitle" class="w-2/3">
    <div v-if="detailData" class="p-6">
      <!-- 基本信息 -->
      <div class="space-y-4">
        <h3 class="border-b pb-2 text-lg font-semibold">基本信息</h3>
        <Descriptions
          bordered
          :column="{ xxl: 2, xl: 2, lg: 2, md: 1, sm: 1, xs: 1 }"
        >
          <Descriptions.Item label="服务企业">
            {{ detailData.enterpriseName || '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="负责人">
            {{ detailData.responsiblePerson || '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="事故类型">
            <DictTag 
              v-if="detailData.accidentType" 
              :type="DICT_TYPE.FIRE_ACCIDENT_TYPE" 
              :value="detailData.accidentType"  
            />
            <span v-else>-</span>
          </Descriptions.Item>
          <Descriptions.Item label="触发火情">
            {{ detailData.fireTrigger || '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="触发时间">
            {{ detailData.departureTime ? formatDateTime(detailData.departureTime) : '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="状态" :span="2">
            <span 
              v-if="detailData.status"
              :style="{ color: getStatusColor(detailData.status), fontWeight: 'bold' }"
            >
              {{ getStatusText(detailData.status) }}
            </span>
            <span v-else>-</span>
          </Descriptions.Item>
        </Descriptions>
      </div>

      <!-- 企业信息 -->
      <Divider orientation="left">企业信息</Divider>
      <div class="space-y-2">
        <div class="flex">
          <span class="w-20 text-gray-600">企业名称:</span>
          <span>{{ detailData.enterpriseName || '-' }}</span>
        </div>
        <div class="flex">
          <span class="w-20 text-gray-600">负责人:</span>
          <span>{{ detailData.responsiblePerson || '-' }}</span>
        </div>
      </div>
    </div>
  </Modal>
</template>
