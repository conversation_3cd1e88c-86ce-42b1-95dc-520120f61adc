<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ProjectInfoApi } from '#/api/thirdparty/projectInfo';
import { useRouter } from 'vue-router';

import { onMounted, onUnmounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromBlobPart, isEmpty } from '@vben/utils';

import { message } from 'ant-design-vue';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteProjectInfo,
  deleteProjectInfoListByIds,
  exportProjectInfo,
  getProjectInfoPage,
} from '#/api/thirdparty/projectInfo';
import { $t } from '#/locales';

import {
  FileTypeEnum,
  getFileTypeDisplayName,
  isFilePreviewable,
  previewFile,
  useGridColumns,
  useGridFormSchema,
} from './data';
import Form from './modules/form.vue';
import ImagePreview from '#/components/preview/ImagePreview.vue';
import OfficePreview from '#/components/preview/OfficePreview.vue';
import PdfPreview from '#/components/preview/PdfPreview.vue';

const router = useRouter();

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

// 预览状态管理
const previewState = ref({
  visible: false,
  type: '' as 'image' | 'pdf' | 'office',
  url: '',
  title: '',
  officeType: 'auto' as 'excel' | 'word' | 'powerpoint' | 'auto',
});

/** 刷新表格 */
function onRefresh() {
  gridApi.query();
}

/** 创建相关方项目信息 */
function handleCreate() {
  formModalApi.setData({}).open();
}

/** 编辑相关方项目信息 */
function handleEdit(row: ProjectInfoApi.ProjectInfo) {
  formModalApi.setData(row).open();
}

/** 查看相关方项目信息详情 */
function handleView(row: ProjectInfoApi.ProjectInfo) {
  router.push(`/thirdparty/projectInfo/detail/${row.id}`);
}

/** 删除相关方项目信息 */
async function handleDelete(row: ProjectInfoApi.ProjectInfo) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.id]),
    key: 'action_key_msg',
  });
  try {
    await deleteProjectInfo(row.id as number);
    message.success({
      content: $t('ui.actionMessage.deleteSuccess', [row.id]),
      key: 'action_key_msg',
    });
    onRefresh();
  } finally {
    hideLoading();
  }
}

/** 批量删除相关方项目信息 */
async function handleDeleteBatch() {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting'),
    key: 'action_key_msg',
  });
  try {
    await deleteProjectInfoListByIds(deleteIds.value);
    message.success({
      content: $t('ui.actionMessage.deleteSuccess'),
      key: 'action_key_msg',
    });
    onRefresh();
  } finally {
    hideLoading();
  }
}

const deleteIds = ref<number[]>([]); // 待删除相关方项目信息 ID
function setDeleteIds({ records }: { records: ProjectInfoApi.ProjectInfo[] }) {
  deleteIds.value = records.map((item) => item.id);
}

/** 导出表格 */
async function handleExport() {
  const data = await exportProjectInfo(await gridApi.formApi.getValues());
  downloadFileFromBlobPart({ fileName: '相关方项目信息.xls', source: data });
}

// 文件预览相关函数
function previewImage(fileUrl: string, fileName?: string) {
  previewState.value = {
    visible: true,
    type: 'image',
    url: fileUrl,
    title: fileName || '项目告知书',
    officeType: 'auto',
  };
  message.success(`正在预览图片: ${fileName || '项目告知书'}`);
}

function previewPDF(fileUrl: string, fileName?: string) {
  previewState.value = {
    visible: true,
    type: 'pdf',
    url: fileUrl,
    title: fileName || '项目告知书',
    officeType: 'auto',
  };
  message.success(`正在预览PDF: ${fileName || '项目告知书'}`);
}

function previewExcel(fileUrl: string, fileName?: string) {
  previewState.value = {
    visible: true,
    type: 'office',
    url: fileUrl,
    title: fileName || '项目告知书',
    officeType: 'excel',
  };
  message.info(`正在使用Office Online预览: ${fileName || '项目告知书'}`);
}

function previewWord(fileUrl: string, fileName?: string) {
  previewState.value = {
    visible: true,
    type: 'office',
    url: fileUrl,
    title: fileName || '项目告知书',
    officeType: 'word',
  };
  message.info(`正在使用Office Online预览: ${fileName || '项目告知书'}`);
}

// 关闭预览
function closePreview() {
  previewState.value.visible = false;
}

// 文件预览主函数
function handlePreview(row: ProjectInfoApi.ProjectInfo) {
  if (!row.projectNotice) {
    message.warning('项目告知书文件不存在，无法预览');
    return;
  }

  // 使用智能预览功能
  const previewResult = previewFile(row.projectNotice, '项目告知书');

  if (!previewResult.success) {
    message.warning(previewResult.message || '不支持预览此类型的文件');
    return;
  }

  // 根据文件类型进行预览
  const plugin = previewResult.plugin!;

  switch (plugin.type) {
    case FileTypeEnum.IMAGE: {
      // 图片预览 - 在新窗口打开
      previewImage(row.projectNotice, '项目告知书');
      break;
    }
    case FileTypeEnum.PDF: {
      // PDF预览 - 在新窗口打开
      previewPDF(row.projectNotice, '项目告知书');
      break;
    }
    case FileTypeEnum.EXCEL: {
      // Excel预览 - 使用在线预览或下载
      previewExcel(row.projectNotice, '项目告知书');
      break;
    }
    case FileTypeEnum.WORD: {
      // Word预览 - 使用在线预览或下载
      previewWord(row.projectNotice, '项目告知书');
      break;
    }
    default: {
      message.warning('暂不支持该文件类型的预览');
    }
  }
}

// 处理文件预览点击事件
function handleFilePreviewClick(event: any) {
  const { row, field } = event.detail;
  if (field === 'projectNotice') {
    handlePreview(row);
  }
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    pagerConfig: {
      enabled: true,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getProjectInfoPage({
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
      isHover: true,
    },
    toolbarConfig: {
      refresh: { code: 'query' },
      search: true,
    },
  } as VxeTableGridOptions<ProjectInfoApi.ProjectInfo>,
  gridEvents: {
    checkboxAll: setDeleteIds,
    checkboxChange: setDeleteIds,
  },
});

// 监听文件预览点击事件
onMounted(() => {
  document.addEventListener('file-preview-click', handleFilePreviewClick);
});

onUnmounted(() => {
  document.removeEventListener('file-preview-click', handleFilePreviewClick);
});
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />

    <!-- 预览组件 -->
    <ImagePreview
      v-if="previewState.visible && previewState.type === 'image'"
      :url="previewState.url"
      :title="previewState.title"
      @close="closePreview"
    />
    <PdfPreview
      v-if="previewState.visible && previewState.type === 'pdf'"
      :url="previewState.url"
      :title="previewState.title"
      @close="closePreview"
    />
    <OfficePreview
      v-if="previewState.visible && previewState.type === 'office'"
      :url="previewState.url"
      :title="previewState.title"
      :type="previewState.officeType"
      @close="closePreview"
    />

    <Grid table-title="相关方项目信息列表">
      <template #toolbar-tools>
        <TableAction
          :actions="[
            {
              label: $t('ui.actionTitle.create', ['相关方项目信息']),
              type: 'primary',
              icon: ACTION_ICON.ADD,
              auth: ['system:project-info:create'],
              onClick: handleCreate,
            },
            {
              label: $t('ui.actionTitle.export'),
              type: 'primary',
              icon: ACTION_ICON.DOWNLOAD,
              auth: ['system:project-info:export'],
              onClick: handleExport,
            },
            {
              label: $t('ui.actionTitle.deleteBatch'),
              type: 'primary',
              danger: true,
              icon: ACTION_ICON.DELETE,
              disabled: isEmpty(deleteIds),
              auth: ['system:project-info:delete'],
              onClick: handleDeleteBatch,
            },
          ]"
        />
      </template>
      <template #actions="{ row }">
        <TableAction
          :actions="[
            {
              label: $t('common.edit'),
              type: 'link',
              icon: ACTION_ICON.EDIT,
              auth: ['system:project-info:update'],
              onClick: handleEdit.bind(null, row),
            },
            {
              label: '详情',
              type: 'link',
              icon: ACTION_ICON.VIEW,
              auth: ['system:project-info:view'],
              onClick: handleView.bind(null, row),
            },
            {
              label: $t('common.delete'),
              type: 'link',
              danger: true,
              icon: ACTION_ICON.DELETE,
              auth: ['system:project-info:delete'],
              popConfirm: {
                title: $t('ui.actionMessage.deleteConfirm', [row.projectName]),
                confirm: handleDelete.bind(null, row),
              },
            },
          ]"
        />
      </template>
    </Grid>
  </Page>
</template>
