<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { AccidentEventsApi } from '#/api/accident/accidentEvents';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromBlobPart, isEmpty } from '@vben/utils';

import { message } from 'ant-design-vue';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteAccidentEvents,
  deleteAccidentEventsListByIds,
  exportAccidentEvents,
  getAccidentEventsPage,
} from '#/api/accident/accidentEvents';
import { $t } from '#/locales';

import { useGridColumns, useGridFormSchema } from './data';
import Detail from './modules/detail.vue';
import Form from './modules/form.vue';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

const [DetailModal, detailModalApi] = useVbenModal({
  connectedComponent: Detail,
  destroyOnClose: true,
});

// 文件预览
function handleFilePreview(fileUrl: string) {
  if (fileUrl) {
    // 在新窗口打开文件
    window.open(fileUrl, '_blank');
  }
}

/** 刷新表格 */
function onRefresh() {
  gridApi.query();
}

/** 创建事故事件信息 */
function handleCreate() {
  formModalApi.setData({}).open();
}

/** 编辑事故事件信息 */
function handleEdit(row: AccidentEventsApi.AccidentEvents) {
  formModalApi.setData(row).open();
}

/** 查看事故事件详情 */
function handleDetail(row: AccidentEventsApi.AccidentEvents) {
  detailModalApi.setData(row).open();
}

/** 删除事故事件信息 */
async function handleDelete(row: AccidentEventsApi.AccidentEvents) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.id]),
    key: 'action_key_msg',
  });
  try {
    await deleteAccidentEvents(row.id as number);
    message.success({
      content: $t('ui.actionMessage.deleteSuccess', [row.id]),
      key: 'action_key_msg',
    });
    onRefresh();
  } finally {
    hideLoading();
  }
}

/** 批量删除事故事件信息 */
async function handleDeleteBatch() {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting'),
    key: 'action_key_msg',
  });
  try {
    await deleteAccidentEventsListByIds(deleteIds.value);
    message.success({
      content: $t('ui.actionMessage.deleteSuccess'),
      key: 'action_key_msg',
    });
    onRefresh();
  } finally {
    hideLoading();
  }
}

const deleteIds = ref<number[]>([]); // 待删除事故事件信息 ID
function setDeleteIds({
  records,
}: {
  records: AccidentEventsApi.AccidentEvents[];
}) {
  deleteIds.value = records.map((item) => item.id);
}

/** 导出表格 */
async function handleExport() {
  const data = await exportAccidentEvents(await gridApi.formApi.getValues());
  downloadFileFromBlobPart({ fileName: '事故事件信息.xls', source: data });
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    pagerConfig: {
      enabled: true,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getAccidentEventsPage({
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
      isHover: true,
    },
    toolbarConfig: {
      refresh: { code: 'query' },
      search: true,
    },
  } as VxeTableGridOptions<AccidentEventsApi.AccidentEvents>,
  gridEvents: {
    checkboxAll: setDeleteIds,
    checkboxChange: setDeleteIds,
  },
});

// 监听文件预览点击事件
document.addEventListener('file-preview-click', (event: any) => {
  const { fileUrl } = event.detail;
  handleFilePreview(fileUrl);
});
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <DetailModal @success="onRefresh" />

    <Grid table-title="事故事件信息列表">
      <template #toolbar-tools>
        <TableAction
          :actions="[
            {
              label: $t('ui.actionTitle.create', ['事故事件信息']),
              type: 'primary',
              icon: ACTION_ICON.ADD,
              auth: ['system:accident-events:create'],
              onClick: handleCreate,
            },
            {
              label: $t('ui.actionTitle.export'),
              type: 'primary',
              icon: ACTION_ICON.DOWNLOAD,
              auth: ['system:accident-events:export'],
              onClick: handleExport,
            },
            {
              label: $t('ui.actionTitle.deleteBatch'),
              type: 'primary',
              danger: true,
              icon: ACTION_ICON.DELETE,
              disabled: isEmpty(deleteIds),
              auth: ['system:accident-events:delete'],
              onClick: handleDeleteBatch,
            },
          ]"
        />
      </template>
      <template #actions="{ row }">
        <TableAction
          :actions="[
            {
              label: '详情',
              type: 'link',
              icon: ACTION_ICON.VIEW,
              onClick: handleDetail.bind(null, row),
            },
            {
              label: $t('common.edit'),
              type: 'link',
              icon: ACTION_ICON.EDIT,
              auth: ['system:accident-events:update'],
              onClick: handleEdit.bind(null, row),
            },
            {
              label: $t('common.delete'),
              type: 'link',
              danger: true,
              icon: ACTION_ICON.DELETE,
              // auth: ['system:accident-events:delete'],
              popConfirm: {
                title: $t('ui.actionMessage.deleteConfirm', [row.accidentName]),
                confirm: handleDelete.bind(null, row),
              },
            },
          ]"
        />
      </template>
    </Grid>
  </Page>
</template>
